#!/usr/bin/env ruby

# Test the extract_file_from_location method
def extract_file_from_location(location)
  # Handle internal Ruby locations like "<internal:timev>:265" or "<internal:pack>:25"
  if location.start_with?('<') && location.include?('>')
    # Find the closing > and include it in the file part
    closing_bracket_index = location.index('>')
    if closing_bracket_index
      return location[0..closing_bracket_index]
    end
  end
  
  # For normal file paths, split on colon and take all parts except the last (line number)
  # This handles cases like "/path/to/file.rb:123"
  parts = location.split(':')
  if parts.length > 1
    # Join all parts except the last one (which should be the line number)
    return parts[0..-2].join(':')
  end
  
  # If no colon found, return the whole location
  location
end

# Test cases
test_cases = [
  "<internal:timev>:265",
  "<internal:pack>:25",
  "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384",
  "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:136",
  "some_file_without_line_number"
]

puts "Testing extract_file_from_location method:"
puts "=" * 50

test_cases.each do |location|
  result = extract_file_from_location(location)
  puts "Input:  #{location}"
  puts "Output: #{result}"
  puts "-" * 30
end
